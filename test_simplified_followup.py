#!/usr/bin/env python3
"""
Test script for simplified follow-up questions functionality using conversation_state table.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.supabase_client import supabase_client
from core.conversation_manager import ConversationManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simplified_followup():
    """Test the simplified follow-up questions functionality."""
    
    logger.info("Testing simplified follow-up questions functionality...")
    
    # Get an existing bot from the database
    bots = supabase_client.get_all_bots()
    if not bots:
        logger.error("❌ No bots found in database. Please create a bot first.")
        return False
    
    # Use the first bot for testing
    test_bot = bots[0]
    test_chat_id = f"{test_bot.id}_simplified_test_user"
    
    logger.info(f"Using bot: {test_bot.name} (ID: {test_bot.id})")
    
    try:
        # Clean up any existing data for this test chat
        supabase_client.reset_conversation(test_chat_id)
        
        # Test 1: Store follow-up questions via conversation manager
        logger.info("Test 1: Storing follow-up questions")
        
        conversation_manager = ConversationManager(test_chat_id, str(test_bot.id))
        test_questions = [
            "What's your favorite coffee?",
            "How did you start your business?", 
            "What challenges did you face?"
        ]
        
        success = conversation_manager.store_follow_up_questions(test_questions)
        if not success:
            logger.error("❌ Failed to store follow-up questions")
            return False
        
        logger.info(f"✅ Stored questions: {test_questions}")
        
        # Test 2: Retrieve follow-up questions
        logger.info("Test 2: Retrieving follow-up questions")
        
        retrieved_questions = conversation_manager.get_follow_up_questions()
        if retrieved_questions == test_questions:
            logger.info(f"✅ Retrieved questions: {retrieved_questions}")
        else:
            logger.error(f"❌ Questions don't match: expected {test_questions}, got {retrieved_questions}")
            return False
        
        # Test 3: Update follow-up questions
        logger.info("Test 3: Updating follow-up questions")
        
        new_questions = ["Tell me about your team", "What's your vision?"]
        success = conversation_manager.store_follow_up_questions(new_questions)
        if not success:
            logger.error("❌ Failed to update follow-up questions")
            return False
        
        updated_questions = conversation_manager.get_follow_up_questions()
        if updated_questions == new_questions:
            logger.info(f"✅ Updated questions: {updated_questions}")
        else:
            logger.error(f"❌ Questions not updated: expected {new_questions}, got {updated_questions}")
            return False
        
        # Test 4: Clear follow-up questions
        logger.info("Test 4: Clearing follow-up questions")
        
        success = conversation_manager.clear_follow_up_questions()
        if not success:
            logger.error("❌ Failed to clear follow-up questions")
            return False
        
        cleared_questions = conversation_manager.get_follow_up_questions()
        if not cleared_questions:
            logger.info("✅ Questions cleared successfully")
        else:
            logger.error(f"❌ Questions still exist: {cleared_questions}")
            return False
        
        # Test 5: Verify conversation state includes follow-up questions
        logger.info("Test 5: Verifying conversation state integration")
        
        # Store some questions again
        final_questions = ["One final question", "Another final question"]
        conversation_manager.store_follow_up_questions(final_questions)
        
        # Get the conversation state directly
        state = supabase_client.get_conversation_state(test_chat_id)
        if state and state.follow_up_questions == final_questions:
            logger.info(f"✅ Conversation state includes follow-up questions: {state.follow_up_questions}")
        else:
            logger.error(f"❌ Conversation state doesn't include questions: {state.follow_up_questions if state else 'No state'}")
            return False
        
        logger.info("🎉 All simplified follow-up tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Cleanup: reset conversation
        try:
            supabase_client.reset_conversation(test_chat_id)
            logger.info("🧹 Cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup failed: {e}")

if __name__ == "__main__":
    success = test_simplified_followup()
    sys.exit(0 if success else 1)
