# Digital Twin MVP - Technical Documentation

## Overview

The Digital Twin MVP is a conversational AI system that creates personalized digital twins based on personal stories and narratives. The system analyzes stories to extract psychological patterns, builds personality profiles, and generates contextually appropriate responses that mirror the original person's communication style and values.

## System Architecture

### Core Design Philosophy

The system follows a **modular, multi-bot architecture** where each digital twin is a separate entity with its own:

- Personality profile derived from story analysis
- Conversation history and state management
- Story corpus and extracted insights
- Welcome messages and call-to-action prompts

### System Flows

The system operates through two distinct flows:

#### **Flow 1: Bot Setup & Training**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Raw Stories    │    │  Setup Scripts  │    │  Bot Config     │
│   (Input)       │    │  (Orchestration)│    │  (Output)       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   Story Deconstructor     │
                    │  (Psychological Analysis) │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   Personality Profiler    │
                    │  (Communication Style)    │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │    Database Storage       │
                    │  (Bot Ready for Chat)     │
                    └───────────────────────────┘
```

#### **Flow 2: Live Conversation**

```
┌─────────────────┐    ┌─────────────────┐
│   User Input    │    │  Telegram Bot   │
│   (Messages)    │    │   Interface     │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   Conversational Engine   │
                    │     (Orchestrator)        │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────▼─────────┐  ┌─────────▼─────────┐  ┌─────────▼─────────┐
│ Conversation      │  │ Story Retrieval   │  │   Bot Profile     │
│ Manager           │  │ Manager           │  │   (Personality)   │
│ (State & Memory)  │  │ (Context/Memory)  │  │                   │
└─────────┬─────────┘  └─────────┬─────────┘  └─────────┬─────────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Response Generation   │
                    │    (Contextual Reply)     │
                    └───────────────────────────┘
```

### **Shared Infrastructure**

```
┌─────────────────┐    ┌─────────────────┐
│   LLM Service   │    │ Database Layer  │
│ (OpenAI API)    │    │   (Supabase)    │
│                 │    │                 │
│ Used by both    │    │ Used by both    │
│ flows for AI    │    │ flows for data  │
│ operations      │    │ persistence     │
└─────────────────┘    └─────────────────┘
```

## Component Breakdown

### **Flow 1: Bot Setup & Training Components**

#### 1. **Story Deconstructor** (`core/story_deconstructor.py`)

**Purpose**: Extracts psychological insights from personal narratives during bot setup
**Current Features**:

- Two-phase analysis pipeline:
  - **Phase 1**: Parallel extraction (triggers, emotions, thoughts)
  - **Phase 2**: Context-aware enrichment (core values)
- Structured data extraction with JSON schemas
- Processes raw story files into structured psychological data

**AI Features**:

- ✅ Emotion detection specialist prompts
- ✅ Cognitive analysis for internal thoughts
- ✅ Values extraction using contextual analysis
- ✅ Trigger identification from story events

#### 2. **Personality Profiler** (`core/personality.py`)

**Purpose**: Generates comprehensive personality profiles from analyzed stories
**Current Features**:

- Raw text analysis approach
- Structured personality extraction covering:
  - Core values and motivations
  - Communication style (formality, tone, sentence structure)
  - Emotional expression patterns
  - Storytelling style and recurring phrases
- Creates bot-specific personality profiles for conversation use

**AI Features**:

- ✅ Multi-dimensional personality analysis
- ✅ Communication pattern recognition
- ✅ Style consistency modeling

#### 3. **Setup Scripts** (`scripts/`)

**Purpose**: Orchestrates the bot creation and training process
**Current Features**:

- Bot configuration management
- Story processing automation
- Database initialization for new bots
- Telegram bot deployment and management

### **Flow 2: Live Conversation Components**

#### 1. **Conversational Engine** (`core/conversational_engine.py`)

**Purpose**: Central orchestrator for live response generation
**Current Features**:

- Multi-bot conversation routing
- Context-aware response generation
- Follow-up question generation (3 per response)
- Call-to-action integration after 5 conversation turns
- Chat ID management for Telegram integration

**AI Features**:

- ✅ Structured response generation with JSON schema validation
- ✅ Dynamic system prompt construction with personality and story context
- ✅ Multi-message conversation history support

#### 2. **Conversation Manager** (`core/conversation_manager.py`)

**Purpose**: Manages conversation state and short-term memory for each chat
**Current Features**:

- Dynamic conversation summarization
- Message persistence and retrieval
- Call-to-action state tracking
- Conversation history truncation
- Per-chat state isolation

**AI Features**:

- ✅ Automatic conversation summarization
- ✅ Context-aware memory management

#### 3. **Story Retrieval Manager** (`core/story_retrieval_manager.py`)

**Purpose**: Intelligent story selection to encode long-term memory into conversations
**Current Features**:

- Judge LLM for story relevance scoring
- Context-aware story filtering based on conversation state
- Option to pass no stories if none are relevant
- Memory integration for contextual responses

**AI Features**:

- ✅ Relevance judgment using conversation summaries
- ✅ Multi-story evaluation and ranking
- ✅ Context-sensitive story selection

### **Shared Infrastructure Components**

#### 1. **LLM Service** (`core/llm_service.py`)

**Purpose**: OpenAI API integration used by both flows
**Current Features**:

- Structured output with JSON schema validation
- Model compatibility checking (gpt-4o, gpt-4o-mini support)
- Fallback mechanisms for unsupported models
- Multi-message conversation support

**AI Features**:

- ✅ JSON schema-enforced structured responses
- ✅ Temperature and token limit controls
- ✅ Robust error handling and fallbacks

#### 2. **Database Layer** (`core/supabase_client.py` & `core/models.py`)

**Purpose**: Data persistence and type-safe operations for both flows
**Current Features**:

- Type-safe dataclasses for all entities
- Bot-specific data isolation
- Conversation history management
- Story and analysis storage

**Database Schema**:

- `bots`: Bot configurations and metadata
- `stories`: Raw story content per bot
- `story_analysis`: Extracted psychological insights
- `personality_profiles`: Generated personality data
- `conversation_history`: Chat messages by chat_id
- `conversation_state`: Conversation summaries and state

#### 3. **Telegram Integration** (`telegram_app/telegram_bot.py`)

**Purpose**: User interface for live conversations
**Current Features**:

- Multi-bot support with separate tokens
- Interactive follow-up questions as clickable buttons
- Rich text formatting with Markdown
- Command support (`/start`, `/help`, `/reset`)
- Typing indicators and error handling
- Bot management script for deployment

## Current AI-Powered Features

### ✅ **Implemented AI Features**

1. **Story Analysis Pipeline**: Multi-phase psychological extraction
2. **Personality Profiling**: Comprehensive communication style analysis
3. **Contextual Response Generation**: Story-aware conversation
4. **Intelligent Story Selection**: Relevance-based context retrieval
5. **Dynamic Conversation Summarization**: Automatic memory management
6. **Structured Output Generation**: JSON schema-enforced responses
7. **Follow-up Question Generation**: Contextual conversation prompts

### 🎯 **AI Replacement Strategy**

**Current State**: Heavy reliance on LLMs for most operations
**Target State**: Replace AI with deterministic logic where possible, keeping LLMs only for natural conversation

**Replacement Candidates**:

- Story categorization → Rule-based classification
- Personality trait extraction → Pattern matching algorithms
- Context selection → Similarity scoring algorithms
- Response templating → Template-based generation with variable substitution

## Recommended Next Steps

### 1. **Image Support Integration** 🎯 _Planned Next_

- Add image processing capabilities to conversation engine
- Support image analysis for story extraction
- Implement multimodal conversation support
- Extend database schema for image storage

### 2. **AI Reduction Initiative**

- **Phase 1**: Replace story categorization with rule-based systems
- **Phase 2**: Implement deterministic personality trait mapping
- **Phase 3**: Create template-based response generation
- **Phase 4**: Develop similarity-based story selection algorithms

### 3. **Enhanced Memory Management**

- Implement conversation truncation strategies
- Add long-term memory persistence
- Create story usage tracking and rotation

### 4. **Production Readiness**

- Add comprehensive error handling and monitoring
- Implement rate limiting and usage controls
- Create deployment automation and scaling strategies
- Add comprehensive testing suite

### 5. **Advanced Features**

- Multi-language support for international deployment
- Voice message integration for richer interaction
- Web interface for broader accessibility
- Advanced analytics and conversation insights

## Technical Specifications

**Languages**: Python 3.11+
**Database**: Supabase (PostgreSQL)
**LLM Provider**: OpenAI (GPT-4o, GPT-4o-mini)
**Integrations**: Telegram Bot API
**Dependencies**: See `requirements.txt` for full list

**Key Libraries**:

- `openai>=1.0.0` - LLM integration
- `supabase>=2.0.0` - Database operations
- `python-telegram-bot>=20.0` - Telegram integration
- `pydantic>=2.0.0` - Data validation

## Deployment

The system supports multiple deployment modes:

- **Production**: Telegram bot deployment with process management
- **Multi-bot**: Concurrent bot management with separate configurations

Bot management is handled through `scripts/telegram_bots.sh` with support for starting, stopping, and monitoring multiple bot instances.
