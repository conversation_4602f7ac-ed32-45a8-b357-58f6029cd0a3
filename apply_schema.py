#!/usr/bin/env python3
"""
Apply database schema changes to Supabase.
This script reads the database_schema.sql file and applies the changes.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.supabase_client import supabase_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def apply_schema():
    """Apply the database schema."""
    
    schema_file = Path("database_schema.sql")
    if not schema_file.exists():
        logger.error("database_schema.sql file not found")
        return False
    
    try:
        # Read the schema file
        with open(schema_file, 'r') as f:
            schema_sql = f.read()
        
        logger.info("Applying database schema...")
        
        # Split the schema into individual statements
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement:
                logger.info(f"Executing statement {i+1}/{len(statements)}")
                try:
                    # Execute the SQL statement
                    result = supabase_client.client.rpc('exec_sql', {'sql': statement}).execute()
                    logger.info(f"✅ Statement {i+1} executed successfully")
                except Exception as e:
                    # Some statements might fail if they already exist, which is okay
                    logger.warning(f"⚠️ Statement {i+1} failed (might already exist): {e}")
        
        logger.info("🎉 Schema application completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to apply schema: {e}")
        return False

if __name__ == "__main__":
    success = apply_schema()
    sys.exit(0 if success else 1)
