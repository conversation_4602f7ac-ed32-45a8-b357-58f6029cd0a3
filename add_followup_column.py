#!/usr/bin/env python3
"""
Add follow_up_questions column to conversation_state table.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_followup_column():
    """Add the follow_up_questions column to conversation_state table."""
    
    logger.info("Please run this SQL in your Supabase SQL editor:")
    print("\n" + "="*60)
    print("ADD FOLLOW-UP QUESTIONS COLUMN")
    print("="*60)
    print("Please run this SQL in your Supabase SQL editor:")
    print()
    print("ALTER TABLE conversation_state")
    print("ADD COLUMN IF NOT EXISTS follow_up_questions TEXT[] DEFAULT '{}';")
    print()
    print("-- Drop the separate follow_up_questions table if it exists")
    print("DROP TABLE IF EXISTS follow_up_questions;")
    print("="*60)
    print()
    print("After running the SQL, press Enter to continue...")
    input()
    
    return True

if __name__ == "__main__":
    success = add_followup_column()
    sys.exit(0 if success else 1)
