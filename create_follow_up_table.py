#!/usr/bin/env python3
"""
Create the follow_up_questions table in Supabase.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.supabase_client import supabase_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_follow_up_questions_table():
    """Create the follow_up_questions table."""
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS follow_up_questions (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        chat_id VARCHAR(255) NOT NULL,
        bot_id UUID REFERENCES bots(id) ON DELETE CASCADE,
        questions TEXT[] NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(chat_id, bot_id)
    );
    """
    
    try:
        logger.info("Creating follow_up_questions table...")
        
        # Execute the SQL using the Supabase client's raw SQL execution
        result = supabase_client.client.postgrest.session.post(
            f"{supabase_client.client.supabase_url}/rest/v1/rpc/exec",
            json={"sql": create_table_sql},
            headers=supabase_client.client.postgrest.session.headers
        )
        
        if result.status_code == 200:
            logger.info("✅ follow_up_questions table created successfully!")
            return True
        else:
            logger.error(f"❌ Failed to create table: {result.status_code} - {result.text}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Failed to create table: {e}")
        return False

if __name__ == "__main__":
    success = create_follow_up_questions_table()
    sys.exit(0 if success else 1)
