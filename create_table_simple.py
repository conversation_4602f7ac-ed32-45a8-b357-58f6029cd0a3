#!/usr/bin/env python3
"""
Simple script to create the follow_up_questions table using direct SQL execution.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.supabase_client import supabase_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_table():
    """Create the follow_up_questions table using a stored procedure approach."""
    
    try:
        logger.info("Attempting to create follow_up_questions table...")
        
        # First, let's try to query the existing tables to see if our table exists
        try:
            result = supabase_client.client.table("follow_up_questions").select("*").limit(1).execute()
            logger.info("✅ follow_up_questions table already exists!")
            return True
        except Exception as e:
            logger.info("Table doesn't exist yet, will create it...")
        
        # Since direct SQL execution might not be available, let's try a different approach
        # We'll create a dummy record to trigger table creation (this won't work, but let's see the error)
        try:
            dummy_data = {
                "chat_id": "test_chat",
                "bot_id": "00000000-0000-0000-0000-000000000000",
                "questions": ["test question"]
            }
            result = supabase_client.client.table("follow_up_questions").insert(dummy_data).execute()
            logger.info("✅ Table exists and is accessible!")
            
            # Clean up the dummy record
            supabase_client.client.table("follow_up_questions").delete().eq("chat_id", "test_chat").execute()
            return True
            
        except Exception as e:
            logger.error(f"❌ Table doesn't exist and cannot be created automatically: {e}")
            logger.info("Please create the table manually in Supabase SQL editor:")
            print("\n" + "="*60)
            print("MANUAL TABLE CREATION REQUIRED")
            print("="*60)
            print("Please run this SQL in your Supabase SQL editor:")
            print()
            print("CREATE TABLE IF NOT EXISTS follow_up_questions (")
            print("    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,")
            print("    chat_id VARCHAR(255) NOT NULL,")
            print("    bot_id UUID REFERENCES bots(id) ON DELETE CASCADE,")
            print("    questions TEXT[] NOT NULL,")
            print("    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),")
            print("    UNIQUE(chat_id, bot_id)")
            print(");")
            print("="*60)
            return False
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = create_table()
    sys.exit(0 if success else 1)
