#!/usr/bin/env python3
"""
Test script for follow-up questions functionality.
Tests the database operations and conversation manager integration.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.supabase_client import supabase_client
from core.conversation_manager import ConversationManager
from core.models import FollowUpQuestions
from uuid import uuid4
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_follow_up_questions():
    """Test follow-up questions functionality."""
    
    # Test data
    test_chat_id = f"test_bot_{uuid4()}_test_user"
    test_bot_id = str(uuid4())
    test_questions = ["What's your favorite memory?", "How did that make you feel?", "What would you do differently?"]
    
    logger.info("Testing follow-up questions functionality...")
    
    try:
        # Test 1: Create and store follow-up questions
        logger.info("Test 1: Creating and storing follow-up questions")
        follow_up_questions = FollowUpQuestions(
            chat_id=test_chat_id,
            bot_id=test_bot_id,
            questions=test_questions,
            created_at=datetime.now(timezone.utc)
        )
        
        stored_questions = supabase_client.upsert_follow_up_questions(follow_up_questions)
        logger.info(f"✅ Stored questions: {stored_questions.questions}")
        
        # Test 2: Retrieve follow-up questions
        logger.info("Test 2: Retrieving follow-up questions")
        retrieved_questions = supabase_client.get_follow_up_questions(test_chat_id)
        if retrieved_questions:
            logger.info(f"✅ Retrieved questions: {retrieved_questions.questions}")
            assert retrieved_questions.questions == test_questions, "Questions don't match!"
        else:
            logger.error("❌ Failed to retrieve questions")
            return False
        
        # Test 3: Test conversation manager integration
        logger.info("Test 3: Testing conversation manager integration")
        conversation_manager = ConversationManager(test_chat_id, test_bot_id)
        
        # Store questions via conversation manager
        new_questions = ["Tell me more about that", "What happened next?", "How did you feel?"]
        success = conversation_manager.store_follow_up_questions(new_questions)
        if success:
            logger.info("✅ Stored questions via conversation manager")
        else:
            logger.error("❌ Failed to store questions via conversation manager")
            return False
        
        # Retrieve questions via conversation manager
        retrieved_via_manager = conversation_manager.get_follow_up_questions()
        if retrieved_via_manager == new_questions:
            logger.info(f"✅ Retrieved questions via conversation manager: {retrieved_via_manager}")
        else:
            logger.error(f"❌ Questions don't match: expected {new_questions}, got {retrieved_via_manager}")
            return False
        
        # Test 4: Clear follow-up questions
        logger.info("Test 4: Clearing follow-up questions")
        success = conversation_manager.clear_follow_up_questions()
        if success:
            logger.info("✅ Cleared questions via conversation manager")
        else:
            logger.error("❌ Failed to clear questions")
            return False
        
        # Verify questions are cleared
        cleared_questions = conversation_manager.get_follow_up_questions()
        if not cleared_questions:
            logger.info("✅ Verified questions are cleared")
        else:
            logger.error(f"❌ Questions still exist: {cleared_questions}")
            return False
        
        logger.info("🎉 All tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Cleanup: try to delete test data
        try:
            supabase_client.delete_follow_up_questions(test_chat_id)
            logger.info("🧹 Cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup failed: {e}")

if __name__ == "__main__":
    success = test_follow_up_questions()
    sys.exit(0 if success else 1)
